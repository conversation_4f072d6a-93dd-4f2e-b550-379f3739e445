import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { cancelMember } from '@/shared-libs/user'

const MU_ID = process.env.NEXT_PUBLIC_MU_ID

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  const { id, reason } = req.body
  try {
    const result = await cancelMember(id, USER_STATUS_ENUM.banned, true, reason)
    if (result?.id) {
      return res.status(200).send({ success: true, data: result })
    }
    throw new Error(`Cann't ban the user.`)
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'User'))
  }
})

export default handler
