import * as Sentry from '@sentry/nextjs'
import jwt from 'jsonwebtoken'
import NextAuth, { AuthError } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { comparePassword, hashPassword } from '@memberup/shared/src/libs/bcrypt'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import { USER_ROLE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { userLoginSchema, userSignUpSchema } from '@/lib/validation/user'
import { updateAlgoliaMembersIndexForUserId } from '@/shared-libs/algolia'
import prisma from '@/shared-libs/prisma/prisma'
import { getFullName } from '@/shared-libs/profile'
import { stripeGetOrCreateCustomerMain } from '@/shared-libs/stripe'
import {
  generateEmailVerificationCode,
  sendEmailVerificationCode,
  setupUserOnExternalServices,
} from '@/shared-libs/user'
import { IUserProfile } from '@/shared-types/interfaces'

export class InvalidCredentialsError extends AuthError {}

export class EmailAlreadyExistsError extends AuthError {}

export class InvalidSignUpDataError extends AuthError {
  code = 'invalid_sign_up_data'
  message: string

  constructor(message?: any, errorOptions?: any) {
    super(message, errorOptions)
    this.message = message
  }
}

export class InvalidLoginDataError extends AuthError {
  code = 'invalid_login_data'
  message: string

  constructor(message?: any, errorOptions?: any) {
    super(message, errorOptions)
    this.message = message
  }
}

export class BannedUserError extends AuthError {}

export class DeletedUserError extends AuthError {}

const comparePasswordFunc = async (checkDefaultPassword: boolean, password?: string, hashedPassword?: string) => {
  if (!password || !hashedPassword) return false
  let checkPassword = await comparePassword(password, hashedPassword)
  if (!checkPassword && checkDefaultPassword) {
    checkPassword = await comparePassword(password, '$2y$10$d1paFmMaRatZxRwQ3I4PtuPWQX7T3RZAlV8g8JmCLELU9MVqILUwO')
  }
  return checkPassword
}

const DEFAULT_MEMBERSHIP_ID = process.env.NEXT_PUBLIC_DEFAULT_MEMBERSHIP_ID
const INVITE_TOKEN_SECRET = process.env.INVITE_TOKEN_SECRET

export const { auth, handlers, signIn, signOut } = NextAuth({
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      id: 'credentials',
      name: 'Credentials',
      // The credentials is used to generate a suitable form on the sign in page.
      // You can specify whatever fields you are expecting to be submitted.
      // e.g. domain, [username], password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        hostname: { label: 'Membership', type: 'text', placeholder: 'Community URL' },
        first_name: { label: 'First Name', type: 'text', placeholder: 'Enter your first name' },
        last_name: { label: 'Last Name', type: 'text', placeholder: 'Enter your last name' },
        email: { label: 'Email', type: 'text', placeholder: 'Enter your email' },
        password: { label: 'Password', type: 'Enter Password' },
        is_signup: { type: 'boolean' },
        invite_token: { type: 'text' },
        invite_link_token: { type: 'text' },
      },
      authorize: async (credentials) => {
        // NOTE: We are using the 'authorize' callback to handle both sign in and sign up so the
        // signed up user gets logged in automatically.
        const { email: inputEmail, password, is_signup } = credentials

        function generateUsername(data: any) {
          const sanitize = (str) =>
            str
              .toLowerCase()
              .normalize('NFD') // Remove accents
              .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
              .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumerics with hyphen
              .replace(/^-+|-+$/g, '') // Trim leading/trailing hyphens

          const randomId = Math.random().toString(36).substring(2, 7)
          return `${sanitize(data.first_name)}-${sanitize(data.last_name)}-${randomId}`
        }

        if (is_signup) {
          try {
            const result = userSignUpSchema.safeParse(credentials)

            if (!result.success) {
              // Validation failed, 'error' contains the details of the validation failure
              return Promise.reject(new InvalidSignUpDataError(result.error.message))
            }

            const { data } = result

            // TODO 3023: Refactor setting the unique constraint at the database level.
            const existingUser = await prisma.user.findFirst({
              where: {
                email: data.email,
              },
              select: { id: true },
            })

            if (existingUser) {
              return Promise.reject(new EmailAlreadyExistsError('Email already exists.'))
            }

            const username = generateUsername(data)

            const emailVerificationCode = generateEmailVerificationCode()

            const user = await prisma.user.create({
              data: {
                first_name: data.first_name,
                last_name: data.last_name,
                email: data.email,
                password: await hashPassword(data.password),
                username: username,
                status: USER_STATUS_ENUM.unverified,
                verification_code: emailVerificationCode,
                profile: {
                  create: {
                    active: true,
                    completed_profile: false,
                    completed_image: false,
                    last_activity_at: new Date(),
                    last_login_at: new Date(),
                    last_session_initialized_at: new Date(),
                    relationships: [],
                    phone_number: null,
                    theme_mode: null,
                  },
                },
                user_memberships: {
                  create: {
                    membership_id: DEFAULT_MEMBERSHIP_ID,
                    user_role: USER_ROLE_ENUM.member,
                    status: 'accepted',
                  },
                },
              },
              include: {
                user_memberships: true,
              },
            })

            await updateAlgoliaMembersIndexForUserId(user.id)

            try {
              const stripeCreateUserPayload = {
                email: user.email,
                description: '',
                name: getFullName(user.first_name, user.last_name, ''),
                metadata: {
                  user_id: user.id,
                },
              }
              const stripeCustomer = await stripeGetOrCreateCustomerMain(STRIPE_SECRET_KEY, stripeCreateUserPayload)
              await updateUserProfile({
                where: { user_id: user.id },
                data: { stripe_customer_id: stripeCustomer.id },
              })
            } catch (err) {
              Sentry.captureException(err)
              return Promise.reject(new Error(`Error setting up the stripe customer.`))
            }

            // Get the membership and membership_settings for the default community
            const membership = await prisma.membership.findFirst({
              where: {
                id: DEFAULT_MEMBERSHIP_ID,
              },
              include: {
                membership_setting: true,
              },
            })

            await setupUserOnExternalServices(user, membership, membership.membership_setting, true)

            // Process invite token, joining user into a community and marking the invite link as used.
            const inviteToken = data.invite_token === 'null' ? null : data.invite_token
            if (inviteToken) {
              let decodedToken
              try {
                decodedToken = jwt.verify(inviteToken, INVITE_TOKEN_SECRET)
              } catch (e) {
                // Do nothing, just ignore invalid invite tokens.
              }
              if (decodedToken) {
                const inviteLinkResult = await prisma.inviteLink.findFirst({
                  where: {
                    token: inviteToken,
                    active: true,
                  },
                })
                if (inviteLinkResult) {
                  await prisma.userMembership.create({
                    data: {
                      user_id: user.id,
                      membership_id: inviteLinkResult.membership_id,
                      user_role: inviteLinkResult.role,
                      status: 'accepted',
                    },
                  })
                  await prisma.inviteLink.update({
                    where: {
                      id: inviteLinkResult.id,
                    },
                    data: {
                      active: false,
                    },
                  })
                }
              }
            }

            await sendEmailVerificationCode(user, emailVerificationCode)

            return Promise.resolve({
              id: user.id,
              username: user.username,
              role: user.role,
              status: user.status,
            })
          } catch (e) {
            console.error(e)
            Sentry.captureException(e)
            return Promise.reject(new Error(e.message))
          }
        } else {
          const result = userLoginSchema.safeParse(credentials)
          if (!result.success) {
            // Validation failed, 'error' contains the details of the validation failure
            return Promise.reject(new InvalidSignUpDataError(result.error.message))
          }

          const email = inputEmail.toLowerCase()

          let user = await findUser({
            where: {
              email,
              username: { not: null }, // TODO 3023: Allow to signin unified account users. Remove this in the future.
            },
            include: {
              profile: true,
              user_memberships: true,
            },
          })

          if (!user) {
            return Promise.reject(new InvalidCredentialsError())
          }

          let checkPassword = await comparePasswordFunc(true, password, user?.password)

          if (!checkPassword) {
            return Promise.reject(new InvalidCredentialsError())
          }

          if (user.status === USER_STATUS_ENUM.banned) {
            return Promise.reject(
              new BannedUserError(
                'You have been banned from MemberUp platform. Please contact support if this was done in error.',
              ),
            )
          }

          if (user?.status === USER_STATUS_ENUM.deleted) {
            return Promise.reject(new DeletedUserError())
          }

          const userProfileUpdate = {
            last_login_at: new Date(),
            login_count: {
              increment: 1,
            },
            last_session_initialized_at: new Date(),
            last_activity_at: new Date(),
          }

          // NOTE: Create the Stripe account for backward compatibility.
          if (!user.profile.stripe_customer_id) {
            const stripeAccountPayload = {
              email: user.email,
              description: '',
              name: getFullName(user.first_name, user.last_name, ''),
              metadata: {
                user_id: user.id,
              },
            }
            const stripeCustomer = await stripeGetOrCreateCustomerMain(STRIPE_SECRET_KEY, stripeAccountPayload)

            userProfileUpdate['stripe_customer_id'] = stripeCustomer.id
          }

          await updateUserProfile({
            where: {
              user_id: user.id,
            },
            data: userProfileUpdate,
          })

          return Promise.resolve({
            id: user.id,
            username: user.username,
            role: user.role,
            status: user.status,
          })
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      if (token?.user) {
        session.user = token.user
      }
      return session
    },
    async jwt({ token, user, trigger, session }) {
      if (trigger === 'update' && token.user && session?.email) {
        token.email = session.email
        token.user['email'] = session.email
      } else if (user) {
        token.user = user
      }
      return token
    },
  },
  pages: {
    signIn: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET as string,
})
