import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@radix-ui/react-visually-hidden'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'
import { useMediaQuery } from 'react-responsive'

import { ChatIcon } from './chat-icon'
import { NotificationsIcon } from './notifications-icon'
import NotificationsMenu from './notifications-menu'
import { UserMenu } from './user-menu'
import { Bell24Icon } from '@/components/icons/24px/bell-24-icon'
import { Search24Icon } from '@/components/icons/24px/search-24-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button } from '@/components/ui'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { useStore } from '@/hooks/useStore'
import { mdMediaQuery } from '@/lib/client/media-queries'
import { getFullName } from '@/lib/formatting'
import { cn } from '@/lib/utils'
import { IUser } from '@/shared-types/interfaces'

enum MOBILE_MENU_SECTIONS {
  chat = 'chat',
  notifications = 'notifications',
  user = 'user',
}

export function TopNavbarMenus() {
  const user = useStore((state) => state.auth.user)
  const profile = useStore((state) => state.auth.profile)
  const knockToken = useStore((state) => state.auth.knockToken)
  const setSearchOpen = useStore((state) => state.ui.setSearchOpen)
  const [notificationsMenuOpen, setNotificationsMenuOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [mobileMenuOpenSection, setMobileMenuOpenSection] = useState<MOBILE_MENU_SECTIONS | null>(null)
  const buttonClass =
    'flex justify-center items-center w-8 h-8 bg-transparent text-black-200 hover:text-black-100 dark:text-black-100 hover:dark:text-black-200 transition-colors'
  const mobileButtonClass = (section: string) =>
    cn(
      'flex justify-center items-center shrink-0 px-4 h-[3.125rem] bg-transparent border-b border-b-[3px] text-black-200 hover:text-black-100 dark:text-black-100 hover:dark:text-black-200 transition-colors',
      section === mobileMenuOpenSection ? 'border-primary-100' : 'border-transparent',
    )
  const isMdRef = useRef(null)
  const isMd = useMediaQuery(mdMediaQuery)

  useEffect(() => {
    if (isMd && !isMdRef.current) {
      isMdRef.current = isMd
      setMobileMenuOpenSection(null)
    } else if (!isMd && isMdRef.current) {
      isMdRef.current = isMd
      setNotificationsMenuOpen(false)
      setUserMenuOpen(false)
    }
  }, [isMd])

  return (
    <div className="flex items-center">
      <div className="hidden items-center space-x-5 md:flex">
        <Button variant="inline" className={buttonClass} onClick={() => setSearchOpen(true)}>
          <Search24Icon />
        </Button>
        <Link className={buttonClass} href="/chat">
          <ChatIcon />
        </Link>
        {knockToken ? (
          <Popover open={notificationsMenuOpen} onOpenChange={setNotificationsMenuOpen}>
            <PopoverTrigger className={cn(buttonClass, 'group')}>
              <NotificationsIcon />
            </PopoverTrigger>
            <PopoverContent className="w-[24.5rem]" align="end">
              <NotificationsMenu onItemClick={() => setNotificationsMenuOpen(false)} />
            </PopoverContent>
          </Popover>
        ) : (
          <div className={buttonClass}>
            <Bell24Icon />
          </div>
        )}
        <Popover open={userMenuOpen} onOpenChange={setUserMenuOpen}>
          <PopoverTrigger className="flex items-center justify-center bg-transparent">
            <ProfilePicture
              className="rounded-full"
              src={profile.image}
              cropArea={profile.image_crop_area}
              alt={getFullName(user as IUser)}
              height={32}
              width={32}
            />
          </PopoverTrigger>
          <PopoverContent className="w-56 rounded-[13.3px]" align="end">
            <UserMenu onItemClick={() => setUserMenuOpen(false)} />
          </PopoverContent>
        </Popover>
      </div>
      <div className="flex items-center space-x-5 md:hidden">
        <Button variant="inline" className={buttonClass} onClick={() => setSearchOpen(true)}>
          <Search24Icon />
        </Button>
        <Button variant="inline" className={buttonClass}>
          <ChatIcon />
        </Button>
        <Button
          variant="inline"
          className={buttonClass}
          onClick={() => setMobileMenuOpenSection(MOBILE_MENU_SECTIONS.notifications)}
        >
          {knockToken ? <NotificationsIcon /> : <Bell24Icon />}
        </Button>
        <Button
          variant="inline"
          className={buttonClass}
          onClick={() => setMobileMenuOpenSection(MOBILE_MENU_SECTIONS.user)}
        >
          <ProfilePicture
            className="h-8 w-8 shrink-0 rounded-full"
            src={profile.image}
            cropArea={profile.image_crop_area}
            alt={getFullName(user as IUser)}
            height={32}
            width={32}
          />
        </Button>
        <Sheet open={Boolean(mobileMenuOpenSection)} onOpenChange={() => setMobileMenuOpenSection(null)}>
          <SheetContent className="space-y-0" hideCloseButton={true}>
            <VisuallyHidden>
              <SheetHeader>
                <SheetTitle>{mobileMenuOpenSection}</SheetTitle>
              </SheetHeader>
            </VisuallyHidden>
            <div className="flex justify-end border-b dark:border-b-grey-900">
              <Link
                href="/chat"
                className={mobileButtonClass(MOBILE_MENU_SECTIONS.chat)}
                onClick={() => setMobileMenuOpenSection(null)}
              >
                <ChatIcon small />
              </Link>
              <Button
                className={mobileButtonClass(MOBILE_MENU_SECTIONS.notifications)}
                variant="inline"
                onClick={() => setMobileMenuOpenSection(MOBILE_MENU_SECTIONS.notifications)}
              >
                <NotificationsIcon small />
              </Button>
              <Button
                className={mobileButtonClass(MOBILE_MENU_SECTIONS.user)}
                variant="inline"
                onClick={() => setMobileMenuOpenSection(MOBILE_MENU_SECTIONS.user)}
              >
                <ProfilePicture
                  className="h-8 w-8 shrink-0 rounded-full"
                  src={profile.image}
                  cropArea={profile.image_crop_area}
                  alt={getFullName(user as IUser)}
                  height={32}
                  width={32}
                />
              </Button>
            </div>
            {mobileMenuOpenSection === MOBILE_MENU_SECTIONS.chat && (
              <NotificationsMenu onItemClick={() => setMobileMenuOpenSection(null)} />
            )}
            {mobileMenuOpenSection === MOBILE_MENU_SECTIONS.notifications && (
              <NotificationsMenu onItemClick={() => setMobileMenuOpenSection(null)} />
            )}
            {mobileMenuOpenSection === MOBILE_MENU_SECTIONS.user && (
              <UserMenu onItemClick={() => setMobileMenuOpenSection(null)} />
            )}
          </SheetContent>
        </Sheet>
      </div>
    </div>
  )
}
