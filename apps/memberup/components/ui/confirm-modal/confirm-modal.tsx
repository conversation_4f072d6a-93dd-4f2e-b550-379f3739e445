import { Button } from '@/components/ui'
import { Modal, ModalContent, ModalDescription, Modal<PERSON>ooter, ModalHeader, ModalTitle } from '@/components/ui/modal'

export function ConfirmModal({
  children,
  onConfirm,
  open,
  onCancel,
  title,
  loading = false,
}: {
  children?: React.ReactNode
  onConfirm?: () => void
  open: boolean
  onCancel?: () => void
  title: string
  loading?: boolean
}) {
  return (
    <Modal open={open} onOpenChange={(value) => !value && onCancel()}>
      <ModalContent overlayAll>
        <ModalHeader>
          <ModalTitle>{title}</ModalTitle>
          <ModalDescription>{children}</ModalDescription>
        </ModalHeader>
        <div className="flex flex-col space-y-5"></div>
        <ModalFooter>
          {onCancel && (
            <Button
              disabled={loading}
              className="w-full sm:w-32"
              variant="outline"
              onClick={(event) => {
                event.stopPropagation()
                onCancel?.()
              }}
            >
              Cancel
            </Button>
          )}
          {onConfirm && (
            <Button
              className="w-full sm:w-32"
              variant="destructive"
              loading={loading}
              disabled={loading}
              onClick={() => {
                onConfirm?.()
              }}
            >
              Confirm
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
