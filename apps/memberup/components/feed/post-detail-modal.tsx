import { Visually<PERSON><PERSON><PERSON> } from '@radix-ui/react-visually-hidden'
import { useEffect, useRef, useState } from 'react'
import { Channel, StreamMessage, useChatContext } from 'stream-chat-react'

import { Close24Icon } from '../icons'
import { PostDetail } from './post-detail'
import { Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog/Dialog'
import { IMembership } from '@/shared-types/interfaces'

export interface PostDetailModalProps {
  membership: IMembership
  feed: StreamMessage
  open: boolean
  onClose: () => void
  onPinMessage: (message: StreamMessage) => Promise<void>
  onUnpinMessage: (message: StreamMessage) => Promise<void>
}

export function PostDetailModal({
  membership,
  feed,
  open,
  onClose,
  onPinMessage,
  onUnpinMessage,
}: PostDetailModalProps) {
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const [editingCommentIds, setEditingCommentIds] = useState<Set<string>>(new Set())
  const [showConfirmModal, setShowConfirmModal] = useState(false)

  const { client } = useChatContext()
  const [channelType, channelId] = feed.cid.split(':')

  const channel = client.channel(channelType, channelId)

  useEffect(() => {
    const pathAfterDomain = window.location.pathname

    if (membership) {
      if (open && feed.permalink) {
        /* this avoids a page reload in contrast to react router replace */
        window.history.replaceState(null, '', `${membership.slug}/post/${feed.permalink || feed.id}`)
      } else {
        window.history.replaceState(null, '', `${membership.slug}/post/${feed.id}`)
      }
    }

    return () => {
      window.history.replaceState(null, '', pathAfterDomain)
    }
  }, [open, feed])

  const descriptionElementRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef
      if (descriptionElement !== null) {
        descriptionElement.focus()
      }
    }
  }, [open])

  const closeModal = () => {
    if (editingCommentIds.size > 0) {
      setShowConfirmModal(true)
    } else {
      onClose()
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent
          variant="plain"
          className="h-screen w-full max-w-full bg-transparent p-0 focus:outline-none sm:max-md:max-h-full md:h-auto md:max-h-[95vh] md:max-w-[720px]"
          data-cy="feed-details-dialog"
          onClick={(e) => {
            e.stopPropagation()
            if (isSubmittingComment) return
            closeModal()
          }}
        >
          <VisuallyHidden>
            <DialogTitle>Post Details</DialogTitle>
          </VisuallyHidden>
          <div id="portal-suggestions-root" />
          <div
            className="flex h-full w-full items-center justify-center overflow-hidden bg-white-500 p-0 dark:bg-black-700 md:items-start md:bg-transparent md:dark:bg-transparent"
            id="post-details-dialog-content"
          >
            <div
              className="h-full w-full md:h-auto"
              id="feed-details-dialog-content-inner"
              onClick={(e) => {
                e.stopPropagation()
              }}
            >
              <div
                id="scroll-dialog-description"
                ref={descriptionElementRef}
                className="h-full focus:outline-none md:h-auto"
              >
                <div className="feed-card-container d-flex h-full flex-col justify-center rounded-[20px] md:h-auto md:justify-start">
                  <Channel channel={channel}>
                    <PostDetail
                      membership={membership}
                      feed={feed}
                      setIsSubmittingComment={setIsSubmittingComment}
                      onPinMessage={onPinMessage}
                      onUnpinMessage={onUnpinMessage}
                      closeModal={closeModal}
                      editingCommentIds={Array.from(editingCommentIds)}
                      addEditingCommentId={(id) => {
                        setEditingCommentIds((prev) => new Set(prev).add(id))
                      }}
                      removeEditingCommentId={(id) => {
                        setEditingCommentIds((prev) => {
                          const newSet = new Set(prev)
                          newSet.delete(id)
                          return newSet
                        })
                      }}
                    />
                  </Channel>
                </div>
              </div>
            </div>
          </div>
          {showConfirmModal && (
            <ConfirmModal
              open={showConfirmModal}
              onCancel={() => setShowConfirmModal(false)}
              onConfirm={onClose}
              title="Confirm Close"
            >
              Are you sure you want to close this post? Any unsaved changes will be lost.
            </ConfirmModal>
          )}
        </DialogContent>
      </Dialog>
      <Button
        className="close-modal absolute right-8 top-8 z-2000 hidden h-10 w-10 items-center justify-center rounded-full bg-white-200 text-black-200 transition-colors hover:bg-grey-400 dark:bg-black-700 dark:text-black-100 dark:hover:bg-grey-900 md:flex"
        variant="inline"
        onClick={closeModal}
      >
        <Close24Icon />
      </Button>
    </>
  )
}
