import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import { Menu, MenuItem, Modal } from '@mui/material'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'

import EditPost from '../../src/components/dialogs/feed/edit-post'
import SVGCloseNew from '../../src/components/svgs/close-new'
import SVGEditNew from '../../src/components/svgs/edit-new'
import SVGPushPinLarge from '../../src/components/svgs/push-pin-filled'
import SVGPushPinLargeEmpty from '../../src/components/svgs/push-pin-large'
import { Verified16Icon } from '../icons/16px/verified-16-icon'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { searchClient } from '@memberup/shared/src/config/algolia-client'
import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { FEED_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import SVGReport from '@/memberup/components/svgs/report'
import SVGTrash from '@/memberup/components/svgs/trash'
import { selectPinnedPostsCount } from '@/memberup/store/features/feedAggregationSlice'
import { reportFeed, setFeedToDelete } from '@/memberup/store/features/feedSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { formatDateLong } from '@/shared-libs/date-utils'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { IChannel, IMembership } from '@/shared-types/interfaces'
import checkStreamUserRole from '@/src/components/hooks/check-stream-user-role'
import { selectMembersMap } from '@/src/store/features/memberSlice'

const useStyles = makeStyles(() => ({
  icons: {
    position: 'relative',
    top: '6px',
    right: '2px',
  },
  iconsMobile: {
    position: 'relative',
    top: '0px',
    right: '0px',
  },
  menuMobile: {
    padding: '15px',
    borderRadius: '8px',
  },
}))

const MAX_PINNED_POSTS = 3

export interface PostHeaderProps {
  className?: string
  feed: IFeed
  isSinglePost?: boolean
  isPostPage?: boolean
  userData: IUser
  showPinnedMessageIndicator?: boolean
  extraHeaderComponents?: React.ReactNode
  onPinMessage?: (feed: IFeed) => void
  onUnpinMessage?: (feed: IFeed) => void
  membership: IMembership
}

export function PostHeader({
  className,
  feed,
  isSinglePost,
  extraHeaderComponents,
  onPinMessage,
  onUnpinMessage,
  membership,
}: PostHeaderProps) {
  const user = useStore((state) => state.auth.user)
  const pinnedPostsCount = useAppSelector((state) => selectPinnedPostsCount(state))
  const dispatch = useAppDispatch()
  const { isMobile, theme } = useAppTheme()
  const mountedRef = useMounted(true)
  const classes = useStyles()
  const [, setCurrentTime] = useState(Date.now()) // This is used to force a re-render of the activity time
  const { isCurrentUserAdmin } = useCheckUserRole()

  const members = useSelector((state: any) => selectMembersMap(state))
  const { isAdminOrCreatorActor } = checkStreamUserRole(feed.user, members)
  const [anchorEl, setAnchorEl] = useState(null)
  const isOwnPost = feed.user.id === user?.id
  const hideContextMenu = !isOwnPost && isAdminOrCreatorActor && user?.role === USER_ROLE_ENUM.member
  const [dateTooltipOpen, setDateTooltipOpen] = useState(false)
  const [editingPost, setEditingPost] = useState(false)
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)

  useEffect(() => {
    if (!mountedRef.current) return
    setCurrentTime(Date.now())
    const interval = setInterval(() => {
      if (mountedRef.current) {
        setCurrentTime(Date.now())
      }
    }, 60000)
    return () => clearInterval(interval)
  }, [feed.created_at, feed.createdAt, mountedRef])

  const handleClickMore = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setAnchorEl(e.currentTarget)
  }

  const handleClose = (e?: React.MouseEvent<HTMLButtonElement>) => {
    e?.preventDefault()
    e?.stopPropagation()
    setAnchorEl(null)
  }
  const handleDelete = () => {
    dispatch(setFeedToDelete(feed))
    searchClient.clearCache()
    handleClose()
  }
  const handleEdit = () => {
    setEditingPost(true)
    dispatch(openDialog({ dialog: 'EditPost', open: true, props: { data: feed, mode: 'edit' } }))
  }
  const handleReport = () => {
    dispatch(
      reportFeed({
        data: {
          id: feed.id as string,
          feed_status: FEED_STATUS_ENUM.reported,
          reports: [
            ...(feed.reports || []),
            {
              date: new Date().toUTCString(),
              name: getFullName(user.first_name, user.last_name, ''),
              email: user.email,
            },
          ],
        },
        messages: {
          success: 'Successfully reported to community admins.',
          fail: 'Failed to report to community admins.',
        },
      }),
    )
  }

  const handlePin = async (pin: boolean) => {
    if (pin) {
      onPinMessage(feed)
    } else {
      onUnpinMessage(feed)
    }
  }

  const createdAt = new Date(feed.created_at || feed.createdAt).getTime()
  const activityTime = getDateTimeFromNow(createdAt)
  const canPinPosts = pinnedPostsCount < MAX_PINNED_POSTS

  const open = Boolean(anchorEl)

  const getSpaceData = (message: IFeed) => {
    const [, channelId] = message.cid.split(':')
    return membership.channels.find((s: IChannel) => s.id === channelId)
  }

  const streamUser = feed.user as IUser
  const name = streamUser?.name || getFullName(streamUser?.first_name, streamUser?.last_name)
  const isFeedCreator = feed.user.id === user?.id
  const editable = isCurrentUserAdmin || isFeedCreator

  const spaceData = getSpaceData(feed)
  if (!spaceData) {
    // NOTE: We don't render the header if we don't have the membership in context yet.
    return null
  }

  const spaceURL = spaceData?.slug ? `${membership.slug}?space=${spaceData.slug}` : ''

  const linkUserProfile = streamUser?.status !== 'deleted' && streamUser?.username

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const UserProfileComponent = ({ children }: { children: React.ReactNode }) =>
    linkUserProfile ? (
      <UserDetailsHoverCard username={streamUser.username}>
        <Link
          className="text-sm font-semibold text-black-700 hover:text-grey-700 dark:text-white-500 hover:dark:text-grey-100"
          href={`/@${streamUser?.username}`}
          onClick={(e) => e.stopPropagation()}
        >
          {children}
        </Link>
      </UserDetailsHoverCard>
    ) : (
      <span className="cursor-default font-semibold text-black-700 dark:text-white-500">{children}</span>
    )

  return (
    <div className={cn('relative flex w-full flex-col justify-between', className)} data-testid="post-header">
      <div>
        <Grid
          container
          alignItems="flex-start"
          className="flex flex-nowrap"
          sx={{
            borderRadius: '16px',
            width: '100%',
            pt: 0,
          }}
        >
          <div className="mr-3.5">
            <AppProfileImage
              className={cn(!linkUserProfile && 'cursor-auto')}
              imageUrl={streamUser?.image || streamUser?.profile?.image || ''}
              cropArea={streamUser?.image_crop_area || streamUser?.profile?.image_crop_area}
              name={name || ''}
              size={40}
            />
          </div>
          <div className="flex grow flex-row">
            <Grid container>
              <Grid item xs={12} className="d-flex align-center">
                <UserProfileComponent>
                  <div className="flex items-center gap-1">
                    {name ? name : 'No Name'}
                    {isAdminOrCreatorActor && <Verified16Icon className="text-community-primary" />}
                  </div>
                </UserProfileComponent>
              </Grid>
              <Grid item xs={12} className="d-flex align-center" sx={{ mt: '1px' }}>
                <TooltipProvider delayDuration={700}>
                  <Tooltip open={dateTooltipOpen} onOpenChange={setDateTooltipOpen}>
                    <TooltipTrigger
                      asChild
                      // Workaround from: https://github.com/radix-ui/primitives/issues/955#issuecomment-**********
                      onClick={() => setDateTooltipOpen((prevOpen) => !prevOpen)}
                      // Timeout runs setOpen after onOpenChange to prevent bug on mobile
                      onFocus={() => setTimeout(() => setDateTooltipOpen(true), 0)}
                      onBlur={() => setDateTooltipOpen(false)}
                    >
                      <div className="text-xs text-black-200 dark:text-black-100">{activityTime}&nbsp;in&nbsp;</div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">{formatDateLong(new Date(feed.created_at))}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Link
                  className="text-xs font-semibold text-black-200 hover:text-black-100 dark:text-black-100 dark:hover:text-black-200"
                  href={spaceURL}
                  onClick={(e) => {
                    e.stopPropagation()
                  }}
                >
                  {spaceData.name}
                </Link>
              </Grid>
            </Grid>
            <div className={''}>{extraHeaderComponents}</div>
          </div>
          {isSinglePost && !hideContextMenu && (
            <Grid item sx={{ paddingRight: { xs: '12px', sm: '0' } }}>
              {isUserAllowedToPost && (
                <IconButton
                  className={cn(
                    open
                      ? 'bg-white-400 text-black-700 dark:bg-grey-900 dark:text-white-500'
                      : 'text-grey-700 hover:text-grey-700 dark:text-black-100 dark:hover:text-white-500',
                  )}
                  size="small"
                  onClick={handleClickMore}
                  aria-label="more"
                  data-cy="post-header-more"
                >
                  <MoreHorizIcon />
                </IconButton>
              )}
            </Grid>
          )}
        </Grid>

        {open &&
          (isMobile ? (
            <Modal
              open={open}
              onClose={handleClose}
              aria-labelledby="slide-dialog-title"
              aria-describedby="slide-dialog-description"
              sx={{
                '& .MuiDialog-paper': {
                  backgroundColor: 'transparent',
                  height: '100%',
                  width: '100%',
                  maxHeight: '100%',
                  maxWidth: '100%',
                  margin: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                },
                '& .MuiMenuItem-root': {
                  p: '12px',
                  color: isDarkTheme ? '#8e94a2' : '#595d65',
                  '&:hover': {
                    color: isDarkTheme ? '#ffffff !important' : '#000000 !important',
                  },
                },
                '& .MuiTypography-body2': {
                  color: isDarkTheme ? '#8e94a2' : '#595d65',
                  fontSize: '12px',
                  lineHeight: '16px',
                },
                '& .MuiListItemText-primary': {
                  fontFamily: 'Graphik Medium',
                  fontSize: '13px',
                  color: isDarkTheme ? '#e1e1e1' : '#1c1c1c',
                  lineHeight: '16px',
                },
              }}
            >
              <div>
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    backgroundColor: isDarkTheme ? '#17171a' : '#fff',
                    width: '100%',
                    margin: 'auto',
                    padding: '10px 10px',
                    borderRadius: '12px 12px 0px 0px',
                  }}
                >
                  <IconButton
                    sx={{
                      background: isDarkTheme ? '#17171a' : '#ffffff',
                      color: '#8D94A3',
                      p: '14px',
                      m: '6px',
                      width: '40px',
                      height: '40px',
                      '&.MuiIconButton-root': {
                        //position: 'initial',
                      },
                      marginTop: '-80px',
                    }}
                    size="medium"
                    aria-label="close"
                    className="close large"
                    onClick={handleClose}
                  >
                    <SVGCloseNew />
                  </IconButton>
                  {isCurrentUserAdmin && (
                    <MenuItem
                      className={clsx(classes.menuMobile, !canPinPosts && !feed.pinned ? 'disabled' : '')}
                      onClick={() => handlePin(!feed.pinned)}
                      data-cy="pin-post"
                      sx={{
                        '&.disabled': {
                          opacity: 0.5,
                          cursor: 'not-allowed',
                        },
                      }}
                    >
                      <ListItemIcon className={classes.iconsMobile}>
                        {feed.pinned ? (
                          <SVGPushPinLarge styles={{ color: theme.palette.primary.main }} />
                        ) : (
                          <SVGPushPinLargeEmpty />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={feed.pinned ? 'Unpin this post' : 'Pin this post'}
                        secondary={!feed.pinned && !canPinPosts ? 'Max 3 pinned posts allowed' : ''}
                      />
                    </MenuItem>
                  )}
                  {editable && (
                    <MenuItem className={classes.menuMobile} onClick={handleEdit} data-cy="edit-post">
                      <ListItemIcon className={classes.iconsMobile}>
                        <SVGEditNew />
                      </ListItemIcon>
                      <ListItemText primary="Edit this post" secondary="Make changes to your post" />
                    </MenuItem>
                  )}
                  {editable && <Divider />}
                  {editable && (
                    <MenuItem className={classes.menuMobile} onClick={handleDelete} data-cy="delete-post">
                      <ListItemIcon className={classes.iconsMobile}>
                        <SVGTrash />
                      </ListItemIcon>
                      <ListItemText primary="Delete this post" secondary="Delete this post permanently" />
                    </MenuItem>
                  )}
                  {!isOwnPost && (
                    <MenuItem className={classes.menuMobile} onClick={handleReport} data-cy="report-post">
                      <ListItemIcon className={classes.iconsMobile}>
                        <SVGReport />
                      </ListItemIcon>
                      <ListItemText primary="Report this post" secondary="Post violates Community Standards" />
                    </MenuItem>
                  )}
                </Box>
              </div>
            </Modal>
          ) : hideContextMenu ? null : (
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              onClick={handleClose}
              sx={{
                '& .MuiBackdrop-root': {
                  backgroundColor: 'transparent',
                },
              }}
              PaperProps={{
                sx: {
                  mt: '6px',
                  width: 265,
                  borderRadius: '12px',
                  boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                  border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                  backgroundColor: isDarkTheme ? '#000000' : '#f7f7f8',
                  padding: '6px 6px 6px 4px',
                  '& .MuiList-root': {
                    paddingTop: 0,
                    paddingBottom: 0,
                  },
                  '& .MuiDivider-root': {
                    marginTop: 1,
                    marginBottom: 1,
                  },
                  '& .MuiMenuItem-root': {
                    p: '12px',
                    alignItems: 'flex-start',
                    color: isDarkTheme ? '#8e94a2' : '#595d65',
                    '&:hover': {
                      color: isDarkTheme ? '#ffffff !important' : '#000000 !important',
                    },
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'inherit',
                    fontSize: 18,
                    minWidth: 28,
                  },
                  '& .MuiSvgIcon-root': {
                    fontSize: 18,
                  },
                  '& .MuiTypography-body2': {
                    color: isDarkTheme ? '#8e94a2' : '#595d65',
                    fontSize: '12px',
                    lineHeight: '16px',
                  },
                  '& .MuiListItemText-primary': {
                    fontFamily: 'Graphik Medium',
                    fontSize: '13px',
                    color: isDarkTheme ? '#e1e1e1' : '#1c1c1c',
                    lineHeight: '16px',
                  },
                  '& .MuiButtonBase-root': {
                    '&:hover': {
                      borderRadius: '8px',
                    },
                  },
                },
              }}
              transformOrigin={{ horizontal: 160, vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              {isCurrentUserAdmin && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                    '&.disabled': {
                      opacity: 0.5,
                      cursor: 'not-allowed',
                    },
                  }}
                  onClick={() => handlePin(!feed.pinned)}
                  data-cy="pin-post"
                  className={!canPinPosts && !feed.pinned ? 'disabled' : ''}
                >
                  <ListItemIcon className={classes.icons}>
                    {feed.pinned ? (
                      <SVGPushPinLarge styles={{ color: theme.palette.primary.main }} />
                    ) : (
                      <SVGPushPinLargeEmpty />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={feed.pinned ? 'Unpin this post' : 'Pin this post'}
                    secondary={!feed.pinned && !canPinPosts ? 'Max 3 pinned posts allowed' : ''}
                  />
                </MenuItem>
              )}

              {editable && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                  }}
                  onClick={handleEdit}
                  data-cy="edit-post"
                >
                  <ListItemIcon className={classes.icons}>
                    <SVGEditNew />
                  </ListItemIcon>
                  <ListItemText primary="Edit this post" secondary="Make changes to your post" />
                </MenuItem>
              )}
              {editable && <Divider />}
              {editable && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                  }}
                  onClick={handleDelete}
                  data-cy="delete-post"
                >
                  <ListItemIcon className={classes.icons}>
                    <SVGTrash />
                  </ListItemIcon>
                  <ListItemText primary="Delete this post" secondary="Delete this post permanently" />
                </MenuItem>
              )}
              {!isOwnPost && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                  }}
                  onClick={handleReport}
                  data-cy="report-post"
                >
                  <ListItemIcon className={classes.icons}>
                    <SVGReport />
                  </ListItemIcon>
                  <ListItemText primary="Report this post" secondary="Post violates Community Standards" />
                </MenuItem>
              )}
            </Menu>
          ))}
      </div>
      {membership?.id && (
        <EditPost
          membership={membership}
          data={feed}
          mode="edit"
          open={editingPost}
          onClose={() => {
            setEditingPost(null)
          }}
        />
      )}
    </div>
  )
}
