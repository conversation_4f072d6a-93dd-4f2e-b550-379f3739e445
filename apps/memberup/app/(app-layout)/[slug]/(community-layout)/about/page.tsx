import dynamic from 'next/dynamic'
import { headers } from 'next/headers'

import { auth } from '@/auth'
import StaticCommunityAboutPage from '@/components/community/about-page/static-about-page'
import { cloudinaryLoader } from '@/lib/cloudinary'
import { getCachedAuthenticatedUserData } from '@/lib/server-components/users'
import { getCachedCommunityData } from '@/lib/server/communities'
import { extractOpenGraphFromHTML } from '@/lib/server/sanitization'
import { checkAdminOrCreatorRole, getUserRole } from '@/shared-libs/profile'

const EditableCommunityAboutPage = dynamic(() => import('@/components/community/about-page/editable-about-page'))

const CommunityAboutPage = async () => {
  const requestHeaders = await headers()
  const session = await auth()
  const communitySlug = requestHeaders.get('x-community-slug')
  let isCurrentUserAdmin = false

  if (session?.user?.id) {
    const userData = await getCachedAuthenticatedUserData(session.user.id)
    const membershipData = await getCachedCommunityData(communitySlug)

    isCurrentUserAdmin = checkAdminOrCreatorRole(getUserRole(userData.user, membershipData.id))
  }

  if (isCurrentUserAdmin) {
    return <EditableCommunityAboutPage />
  }

  return <StaticCommunityAboutPage />
}

export default CommunityAboutPage

export async function generateMetadata() {
  const requestHeaders = await headers()
  const communitySlug = requestHeaders.get('x-community-slug')
  const membershipData = await getCachedCommunityData(communitySlug)

  let image: null | { url: string; width: number; height: number; alt: string } = null

  if ((membershipData?.membership_setting?.about_gallery as Array<Object>)?.length > 0) {
    const firstImage = membershipData.membership_setting.about_gallery?.[0]

    if (firstImage?.url) {
      const thumbnailUrl = cloudinaryLoader({
        loaderOptions: {
          src: firstImage?.url,
          width: 1200,
          quality: 80,
        },
        preTransforms: `c_crop,w_1200,h_630`,
      })
      image = {
        url: thumbnailUrl,
        width: firstImage.width,
        height: firstImage.height,
        alt: membershipData.name,
      }
    } else if (firstImage?.mux_asset?.playback_ids?.[0]?.id) {
      image = {
        url: `https://image.mux.com/${firstImage?.mux_asset?.playback_ids?.[0]?.id}/thumbnail.jpg?width=1200&height=630&fit_mode=crop`,
        width: 1200,
        height: 630,
        alt: membershipData.name,
      }
    }
  }

  const aboutText = membershipData?.membership_setting?.about_text
  const ogDescription = await extractOpenGraphFromHTML(aboutText)

  return {
    title: membershipData?.name,
    description: membershipData?.membership_setting?.about_text,
    openGraph: {
      title: membershipData?.name,
      description: ogDescription,
      type: 'website',
      images: image ? [image] : [],
      siteName: 'MemberUp',
    },
    twitter: {
      card: 'summary_large_image',
      title: membershipData?.name,
      description: ogDescription,
      images: image ? [image.url] : [],
    },
  }
}
