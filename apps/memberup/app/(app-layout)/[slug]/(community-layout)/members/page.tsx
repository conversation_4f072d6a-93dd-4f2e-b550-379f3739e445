'use client'

import { useSearchParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { CommunityDetails } from '@/components/community/community-details'
import { InviteSettings } from '@/components/settings/member-settings/invite-settings'
import { MembershipSettings } from '@/components/settings/member-settings/membership-settings'
import { NotificationsSettings } from '@/components/settings/member-settings/notification-settings'
import { Button, SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useStore } from '@/hooks/useStore'
import { formatThousands } from '@/lib/formatting'
import { checkAdminOrCreatorRole } from '@/shared-libs/profile'
import { approveAllMembersRequestsApi, getMemberRequestsApi } from '@/shared-services/apis/membership.api'
import { deleteUserApi } from '@/shared-services/apis/user.api'
import useCheckUserRole from '@/src/components/hooks/use-check-user-role'
import MembersListing from '@/src/components/member/members-listing-new'
import MembersRequests from '@/src/components/member/members-requests'
import { getMembers, selectMembersMap } from '@/src/store/features/memberSlice'
import { openDialog } from '@/src/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

const DEFAULT_TAB = 'members'

export default function MembersPage() {
  const searchParams = useSearchParams()
  const tab = searchParams.get('tab')
  const [loadingMembersRequests, setLoadingMembersRequestsMembersRequests] = useState(false)
  const dispatch = useAppDispatch()
  const membersMap = useAppSelector((state) => selectMembersMap(state))
  const { isCurrentUserAdmin } = useCheckUserRole()
  const [currentTab, setCurrentTab] = useState(DEFAULT_TAB)
  const [openConfirm, setOpenConfirm] = useState(false)
  const membership = useStore((state) => state.community.membership)

  const [membersRequests, setMembersRequests] = useState([])
  const [selectedUserId, setSelectedUserId] = useState(null)
  const [requestDeleteMember, setRequestDeleteMember] = useState(false)

  useEffect(() => {
    if (!membership) return

    const initialize = async () => {
      setLoadingMembersRequestsMembersRequests(true)
      const res = await getMemberRequestsApi(membership.id)
      setMembersRequests(res.data.data)
      setLoadingMembersRequestsMembersRequests(false)
    }
    initialize()
  }, [membership])

  useEffect(() => {
    if (!membership) {
      return
    }

    if (tab && typeof tab === 'string') {
      setCurrentTab(tab)
    } else {
      setCurrentTab(DEFAULT_TAB)
    }
    dispatch(
      getMembers({
        membershipId: membership?.id,
        where: JSON.stringify({
          status: USER_STATUS_ENUM.active,
        }),
        take: 10000,
        skip: 0,
      }),
    )
  }, [membership])

  const handleMembersRequestsChanged = () => {
    // Reload members
  }

  const allUsers = Object.values(membersMap)
  const admins = allUsers.filter((m: any) => checkAdminOrCreatorRole(m?.role))
  const members = allUsers.filter((m: any) => !checkAdminOrCreatorRole(m?.role))
  const onlineMembers = allUsers.filter((m: any) => m.isOnline)

  const membersCount = members.length
  const adminsCount = admins.length
  const onlineCount = onlineMembers.length
  const requestsCount = membersRequests.length

  const showInviteButton = currentTab !== 'requests' && isCurrentUserAdmin
  const showApproveAll = currentTab === 'requests' && isCurrentUserAdmin
  const isApproveAllDisabled = requestsCount === 0

  const handleInviteMember = () => {
    const config = {
      sections: [
        {
          name: 'membership',
          title: 'Membership',
          component: MembershipSettings,
        },
        {
          name: 'notifications',
          title: 'Notifications',
          component: NotificationsSettings,
        },
        {
          name: 'invite',
          title: 'Invite',
          component: InviteSettings,
        },
      ],
    }
    dispatch(openDialog({ dialog: 'MemberSettings', open: true, props: { defaultSection: 'invite' } }))
  }
  const handleApproveAll = async () => {
    setOpenConfirm(true)
  }

  const handleApproveAllConfirm = async () => {
    setLoadingMembersRequestsMembersRequests(true)
    await approveAllMembersRequestsApi(membership.id)
    const res = await getMemberRequestsApi(membership.id)
    setMembersRequests(res.data.data)
    setLoadingMembersRequestsMembersRequests(false)
    setOpenConfirm(false)
  }

  const handleApproveAllClose = async () => {
    setOpenConfirm(false)
  }

  const handleOnDeleteMemberClick = (id) => {
    setSelectedUserId(id)
  }

  const handleOnDeleteConfirm = async () => {
    setRequestDeleteMember(true)
    try {
      const result = await deleteUserApi(membership.id, selectedUserId, false)
      if (result.data.success) {
        toast.success('Member removed successfully.')
        dispatch(
          getMembers({
            membershipId: membership.id,
            where: JSON.stringify({
              status: USER_STATUS_ENUM.active,
            }),
            take: 10000,
            skip: 0,
          }),
        )
      }
    } catch (e) {
      toast.error(e.message)
    } finally {
      setRequestDeleteMember(false)
      setSelectedUserId(null)
    }
  }

  return (
    <div className="space-y:6 page-inner-pb mobile-padded-content-container flex flex-col-reverse items-start md:flex-row md:space-x-6 md:space-y-0">
      <ConfirmModal
        title="Are you sure you want to remove the member?"
        onConfirm={handleOnDeleteConfirm}
        loading={requestDeleteMember}
        open={selectedUserId}
        onCancel={() => setSelectedUserId(null)}
      />
      <div className="flex-grow">
        <div>
          <div className="text-white font-['Graphik'] text-lg font-semibold leading-normal">Members</div>
          <div className="mt-5">
            {!loadingMembersRequests && (
              <div className="flex-grow">
                <Tabs defaultValue={DEFAULT_TAB} value={currentTab} onValueChange={(value) => setCurrentTab(value)}>
                  <div className="flex items-center">
                    <div className="grow">
                      <TabsList>
                        <TabsTrigger value="members">Members ({formatThousands(membersCount)})</TabsTrigger>
                        <TabsTrigger value="admins">Admins ({formatThousands(adminsCount)})</TabsTrigger>
                        <TabsTrigger value="online">Online ({formatThousands(onlineCount)})</TabsTrigger>
                        {isCurrentUserAdmin && (
                          <TabsTrigger value="requests">Requests ({formatThousands(requestsCount)})</TabsTrigger>
                        )}
                      </TabsList>
                    </div>
                    <div>
                      {showInviteButton && (
                        <Button
                          className="w-[69px]"
                          type="submit"
                          variant="default"
                          size="sm"
                          onClick={handleInviteMember}
                          data-cy="invite-button"
                        >
                          Invite
                        </Button>
                      )}
                      {showApproveAll && (
                        <Button
                          type="submit"
                          size="sm"
                          variant="default"
                          disabled={isApproveAllDisabled || loadingMembersRequests}
                          onClick={handleApproveAll}
                          data-cy="approval-all-button"
                        >
                          Approve All
                        </Button>
                      )}
                    </div>
                  </div>
                  <TabsContent value="members">
                    <MembersListing members={members} onDeleteMemberClick={handleOnDeleteMemberClick} />
                  </TabsContent>
                  <TabsContent value="admins">
                    <MembersListing members={admins} onDeleteMemberClick={handleOnDeleteMemberClick} />
                  </TabsContent>
                  <TabsContent value="online">
                    <MembersListing members={onlineMembers} onDeleteMemberClick={handleOnDeleteMemberClick} />
                  </TabsContent>
                  <TabsContent value="requests">
                    <MembersRequests
                      membersRequests={membersRequests}
                      setMembersRequests={setMembersRequests}
                      onMemberRequestsChanged={handleMembersRequestsChanged}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </div>
          {loadingMembersRequests && <SkeletonBox />}
        </div>
      </div>
      <CommunityDetails />

      <ConfirmModal
        title="Please confirm"
        open={openConfirm}
        onCancel={() => handleApproveAllClose()}
        onConfirm={() => handleApproveAllConfirm()}
      >
        Are you sure you want to approve all members requests?
      </ConfirmModal>
    </div>
  )
}
