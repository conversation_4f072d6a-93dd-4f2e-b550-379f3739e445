import { useEffect } from 'react'
import { useInstantSearch } from 'react-instantsearch'

import { useStore } from '@/hooks/useStore'
import PostSearchResultList from '@/memberup/components/algolia/post-search-result-list'
import SearchEmptyState from '@/memberup/components/algolia/search-empty-state'
import { setSearchStatus } from '@/memberup/store/features/uiSlice'
import { useAppDispatch } from '@/memberup/store/hooks'

const FeedHits = (props: { searchResults: any; displayEmptyResult: boolean; members: any }) => {
  const { searchResults, displayEmptyResult, members } = props
  const setSearchOpen = useStore((state) => state.ui.setSearchOpen)

  const dispatch = useAppDispatch()

  const onClickSearchResultHandler = () => {
    setSearchOpen(false)
  }

  useEffect(() => {
    if (searchResults?.hits?.length) {
      dispatch(setSearchStatus({ searchIndex: 'feed', hasResults: true }))
    } else {
      dispatch(setSearchStatus({ searchIndex: 'feed', hasResults: false }))
    }
  }, [searchResults])

  return (
    <>
      {searchResults?.hits?.length ? (
        <>
          <div className="mx-auto space-y-3">
            <div className="w-full">
              <div className="flex items-center space-x-1">
                <div>
                  <h6 className="text-black mb-2 text-sm font-semibold">Posts</h6>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full">
            <PostSearchResultList
              searchResults={searchResults}
              onClickSearchResultHandler={onClickSearchResultHandler}
              members={members}
            />
          </div>
        </>
      ) : null}
      {!searchResults?.hits?.length && displayEmptyResult && (
        <div className="mt-5">
          <SearchEmptyState />
        </div>
      )}
    </>
  )
}

function CustomSearchBox(props: { displayEmptyResult: boolean; members: any }) {
  const { results } = useInstantSearch({
    catchError: true,
  })

  return <FeedHits displayEmptyResult={props.displayEmptyResult} searchResults={results} members={props.members} />
}

export default CustomSearchBox
